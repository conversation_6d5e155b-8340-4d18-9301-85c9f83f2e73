#!/usr/bin/env python3
"""Simple test for comprehensive email analysis."""

import json
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_comprehensive_analysis():
    """Test the comprehensive analysis with a simple email."""
    
    print("Testing Comprehensive Email Analysis")
    print("=" * 40)
    
    try:
        from config.settings import set_environment
        from config.logging_config import setup_logging
        from core.processor import EmailProcessor
        from core.models import EmailData
        
        # Set up environment
        set_environment('local')
        setup_logging(log_level='INFO', environment='local')
        
        print("1. Initializing EmailProcessor...")
        processor = EmailProcessor(user_email="<EMAIL>")
        print("   ✓ EmailProcessor initialized")
        
        print("2. Creating test email data...")
        test_email = EmailData(
            from_field="<EMAIL>",
            to_field="<EMAIL>",
            subject="Welcome to Shopify!",
            body="""
            Welcome to Shopify! Your store is now ready.
            
            You can access your admin panel at https://admin.shopify.com
            
            If you need help, contact our support team.
            
            Best regards,
            The Shopify Team
            """,
            date="2023-12-01"
        )
        print("   ✓ Test email created")
        
        print("3. Running comprehensive analysis...")
        result = processor.analyze_email_comprehensive(test_email)
        print("   ✓ Analysis completed")
        
        print("\n📊 Analysis Results:")
        print(f"   📧 Category: {result.get('email_category', 'N/A')}")
        print(f"   🏢 Business: {result.get('business_entity_name', 'N/A')}")
        print(f"   📬 Email: {result.get('business_entity_email', 'N/A') or 'Not found'}")
        print(f"   🌐 Website: {result.get('business_entity_website', 'N/A') or 'Not found'}")
        print(f"   🛡️ DPO: {result.get('business_entity_dpo_email', 'N/A') or 'Not found'}")
        
        print("\n📋 Full JSON Response:")
        print(json.dumps(result, indent=2))
        
        # Validate required fields
        required_fields = [
            "email_category",
            "business_entity_name", 
            "business_entity_email",
            "business_entity_website",
            "business_entity_dpo_email"
        ]
        
        print("\n✅ Field Validation:")
        all_present = True
        for field in required_fields:
            if field in result:
                print(f"   ✓ {field}: Present")
            else:
                print(f"   ❌ {field}: Missing")
                all_present = False
        
        if all_present:
            print("\n🎉 All required fields are present!")
            return True
        else:
            print("\n❌ Some required fields are missing!")
            return False
        
    except Exception as e:
        print(f"\n❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cli_command():
    """Test the CLI command for comprehensive analysis."""
    
    print("\nTesting CLI Command")
    print("=" * 40)
    
    # Check if sample email exists
    sample_email = "emails/business_meeting.eml"
    if not Path(sample_email).exists():
        print(f"⚠️  Sample email not found: {sample_email}")
        print("   Skipping CLI test")
        return True
    
    print("CLI Usage Example:")
    print(f"   python cli.py --user-email <EMAIL> process-comprehensive {sample_email}")
    print("\nTo test manually, run the above command.")
    
    return True

if __name__ == "__main__":
    print("Email Scanner - Simple Comprehensive Analysis Test")
    print("=" * 60)
    
    success = True
    
    # Test comprehensive analysis
    if not test_comprehensive_analysis():
        success = False
    
    # Show CLI usage
    if not test_cli_command():
        success = False
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("\nNext steps:")
        print("1. Try the CLI command shown above")
        print("2. Test with different email files")
        print("3. Check the web search functionality")
        sys.exit(0)
    else:
        print("\n❌ Test failed!")
        sys.exit(1)
