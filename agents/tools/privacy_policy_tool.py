"""Enhanced privacy policy scraping tool for LLM chains."""

import re
import logging
import requests
from typing import Optional, Dict, List, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from dataclasses import dataclass
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class PrivacyPolicyInput(BaseModel):
    """Input for privacy policy tool."""
    website_url: str = Field(description="Website URL to find privacy policy for")
    extract_dpo: bool = Field(default=True, description="Whether to extract DPO contact information")


@dataclass
class PrivacyPolicyResult:
    """Result from privacy policy scraping."""
    privacy_policy_url: Optional[str] = None
    dpo_email: Optional[str] = None
    privacy_email: Optional[str] = None
    contact_email: Optional[str] = None
    data_practices: List[str] = None
    last_updated: Optional[str] = None
    
    def __post_init__(self):
        if self.data_practices is None:
            self.data_practices = []


class EnhancedPrivacyPolicyTool(BaseTool):
    """Enhanced tool for finding and analyzing privacy policies with DPO extraction."""

    name: str = "privacy_policy_scraper"
    description: str = "Find privacy policy URLs and extract DPO contact information and data practices"
    args_schema: type[BaseModel] = PrivacyPolicyInput
    timeout: int = Field(default=30, description="Request timeout in seconds")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'logger', logging.getLogger(self.__class__.__name__))
        
        # Common privacy policy URL patterns
        object.__setattr__(self, 'privacy_url_patterns', [
            '/privacy',
            '/privacy-policy',
            '/privacy.html',
            '/legal/privacy',
            '/privacy-notice',
            '/privacypolicy',
            '/terms-privacy',
            '/legal/privacy-policy',
            '/privacy-statement',
            '/data-protection'
        ])

    def _run(self, website_url: str, extract_dpo: bool = True) -> str:
        """Find privacy policy and extract information."""
        try:
            self.logger.info(f"Finding privacy policy for: {website_url}")
            
            # Normalize URL
            if not website_url.startswith(('http://', 'https://')):
                website_url = 'https://' + website_url
            
            result = PrivacyPolicyResult()
            
            # Find privacy policy URL
            privacy_url = self._find_privacy_policy_url(website_url)
            if not privacy_url:
                return f"No privacy policy found for {website_url}"
            
            result.privacy_policy_url = privacy_url
            
            # Extract information from privacy policy
            if extract_dpo:
                self._extract_privacy_info(privacy_url, result)
            
            # Format result as string for LLM
            return self._format_result(result)
            
        except Exception as e:
            error_msg = f"Error processing privacy policy for {website_url}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"

    def _find_privacy_policy_url(self, base_url: str) -> Optional[str]:
        """Find privacy policy URL for a website."""
        try:
            parsed_url = urlparse(base_url)
            base_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # Try common privacy policy URL patterns
            for pattern in self.privacy_url_patterns:
                privacy_url = urljoin(base_domain, pattern)
                
                try:
                    response = requests.head(privacy_url, timeout=self.timeout)
                    if response.status_code == 200:
                        self.logger.info(f"Found privacy policy at: {privacy_url}")
                        return privacy_url
                except requests.RequestException:
                    continue
            
            # Try to find privacy policy link on the main page
            try:
                response = requests.get(base_url, timeout=self.timeout)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Look for privacy policy links
                    privacy_links = soup.find_all('a', href=True)
                    for link in privacy_links:
                        href = link.get('href', '').lower()
                        text = link.get_text().lower()
                        
                        if any(keyword in href or keyword in text for keyword in 
                               ['privacy', 'privacypolicy', 'privacy-policy', 'data protection']):
                            privacy_url = urljoin(base_url, link['href'])
                            self.logger.info(f"Found privacy policy link: {privacy_url}")
                            return privacy_url
            
            except requests.RequestException as e:
                self.logger.warning(f"Could not access main page {base_url}: {e}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding privacy policy URL: {e}")
            return None

    def _extract_privacy_info(self, privacy_url: str, result: PrivacyPolicyResult):
        """Extract information from privacy policy page."""
        try:
            response = requests.get(privacy_url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract text content
            text_content = self._extract_text_content(soup)
            
            # Extract DPO and contact information
            self._extract_contact_info(text_content, result)
            
            # Extract data practices
            self._extract_data_practices(text_content, result)
            
            # Extract last updated date
            result.last_updated = self._extract_last_updated(soup, text_content)
            
        except Exception as e:
            self.logger.warning(f"Error extracting privacy info from {privacy_url}: {e}")

    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """Extract clean text content from the page."""
        try:
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception as e:
            self.logger.warning(f"Error extracting text content: {e}")
            return ""

    def _extract_contact_info(self, text: str, result: PrivacyPolicyResult):
        """Extract contact information including DPO emails."""
        try:
            text_lower = text.lower()
            
            # Email pattern
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, text, re.IGNORECASE)
            
            # Categorize emails
            for email in emails:
                email_lower = email.lower()
                
                # Check for DPO-specific emails
                if any(keyword in email_lower for keyword in ['dpo', 'data.protection', 'dataprotection']):
                    result.dpo_email = email
                    break
                # Check for privacy-related emails
                elif any(keyword in email_lower for keyword in ['privacy', 'legal']):
                    if not result.privacy_email:
                        result.privacy_email = email
                # General contact emails
                elif any(keyword in email_lower for keyword in ['contact', 'info', 'support']):
                    if not result.contact_email:
                        result.contact_email = email
            
            # If no DPO email found, use privacy email as fallback
            if not result.dpo_email and result.privacy_email:
                result.dpo_email = result.privacy_email
                
        except Exception as e:
            self.logger.warning(f"Error extracting contact info: {e}")

    def _extract_data_practices(self, text: str, result: PrivacyPolicyResult):
        """Extract data collection practices."""
        try:
            text_lower = text.lower()
            practices = []
            
            # Common data practice keywords
            practice_keywords = {
                'personal_information': ['personal information', 'personal data', 'pii'],
                'cookies': ['cookies', 'tracking', 'analytics'],
                'location': ['location', 'geolocation', 'gps'],
                'device_info': ['device information', 'browser', 'ip address'],
                'third_party': ['third party', 'partners', 'vendors']
            }
            
            for category, keywords in practice_keywords.items():
                for keyword in keywords:
                    if keyword in text_lower:
                        practices.append(f"{category}: {keyword} mentioned")
                        break
            
            result.data_practices = practices
            
        except Exception as e:
            self.logger.warning(f"Error extracting data practices: {e}")

    def _extract_last_updated(self, soup: BeautifulSoup, text: str) -> Optional[str]:
        """Extract last updated date."""
        try:
            # Look for common date patterns
            date_patterns = [
                r'last updated:?\s*([a-zA-Z]+ \d{1,2},? \d{4})',
                r'effective date:?\s*([a-zA-Z]+ \d{1,2},? \d{4})',
                r'updated:?\s*([a-zA-Z]+ \d{1,2},? \d{4})',
                r'(\d{1,2}/\d{1,2}/\d{4})',
                r'(\d{4}-\d{2}-\d{2})'
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    return match.group(1)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error extracting last updated date: {e}")
            return None

    def _format_result(self, result: PrivacyPolicyResult) -> str:
        """Format result as string for LLM consumption."""
        output = []
        
        if result.privacy_policy_url:
            output.append(f"Privacy Policy URL: {result.privacy_policy_url}")
        
        if result.dpo_email:
            output.append(f"DPO Email: {result.dpo_email}")
        
        if result.privacy_email and result.privacy_email != result.dpo_email:
            output.append(f"Privacy Email: {result.privacy_email}")
        
        if result.contact_email:
            output.append(f"Contact Email: {result.contact_email}")
        
        if result.last_updated:
            output.append(f"Last Updated: {result.last_updated}")
        
        if result.data_practices:
            output.append(f"Data Practices: {', '.join(result.data_practices)}")
        
        return '\n'.join(output) if output else "No privacy policy information found"
