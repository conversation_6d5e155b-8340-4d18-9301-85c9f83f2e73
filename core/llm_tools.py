"""Configurable LLM tools for business identification."""
import logging
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod

from config.settings import get_config
from config.logging_config import LoggerMixin
from agents.tools import WebScrapingTool, DomainLookupTool, BusinessSearchTool, EnhancedPrivacyPolicyTool


class LLMTool(ABC):
    """Abstract base class for LLM tools."""
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the tool name."""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get the tool description."""
        pass
    
    @abstractmethod
    def is_enabled(self) -> bool:
        """Check if the tool is enabled in configuration."""
        pass
    
    @abstractmethod
    def execute(self, **kwargs) -> str:
        """Execute the tool with given parameters."""
        pass


class ConfigurableWebSearchTool(LLMTool, LoggerMixin):
    """Web search tool that can be enabled/disabled via configuration."""
    
    def __init__(self):
        self.config = get_config()
        self.tool = WebScrapingTool() if self.is_enabled() else None
    
    def get_name(self) -> str:
        return "web_search"
    
    def get_description(self) -> str:
        return "Search and scrape web pages for business information"
    
    def is_enabled(self) -> bool:
        return self.config.llm_tools.enable_web_search
    
    def execute(self, url: str, extract_contacts: bool = True) -> str:
        if not self.is_enabled():
            return "Web search tool is disabled in configuration"
        
        try:
            return self.tool._run(url, extract_contacts)
        except Exception as e:
            self.logger.error(f"Web search tool error: {e}")
            return f"Error: {str(e)}"


class ConfigurableDomainLookupTool(LLMTool, LoggerMixin):
    """Domain lookup tool that can be enabled/disabled via configuration."""
    
    def __init__(self):
        self.config = get_config()
        self.tool = DomainLookupTool() if self.is_enabled() else None
    
    def get_name(self) -> str:
        return "domain_lookup"
    
    def get_description(self) -> str:
        return "Look up domain information and DNS records"
    
    def is_enabled(self) -> bool:
        return self.config.llm_tools.enable_domain_lookup
    
    def execute(self, email_or_domain: str) -> str:
        if not self.is_enabled():
            return "Domain lookup tool is disabled in configuration"
        
        try:
            return self.tool._run(email_or_domain)
        except Exception as e:
            self.logger.error(f"Domain lookup tool error: {e}")
            return f"Error: {str(e)}"


class ConfigurableBusinessSearchTool(LLMTool, LoggerMixin):
    """Business search tool that can be enabled/disabled via configuration."""
    
    def __init__(self):
        self.config = get_config()
        self.tool = BusinessSearchTool() if self.is_enabled() else None
    
    def get_name(self) -> str:
        return "business_search"
    
    def get_description(self) -> str:
        return "Search for business information using various APIs"
    
    def is_enabled(self) -> bool:
        return self.config.llm_tools.enable_business_search
    
    def execute(self, business_name: str, additional_info: str = "") -> str:
        if not self.is_enabled():
            return "Business search tool is disabled in configuration"
        
        try:
            return self.tool._run(business_name, additional_info)
        except Exception as e:
            self.logger.error(f"Business search tool error: {e}")
            return f"Error: {str(e)}"


class ConfigurablePrivacyScrapingTool(LLMTool, LoggerMixin):
    """Privacy policy scraping tool that can be enabled/disabled via configuration."""
    
    def __init__(self):
        self.config = get_config()
        self.tool = EnhancedPrivacyPolicyTool() if self.is_enabled() else None
    
    def get_name(self) -> str:
        return "privacy_scraping"
    
    def get_description(self) -> str:
        return "Find and scrape privacy policies for DPO information"
    
    def is_enabled(self) -> bool:
        return self.config.llm_tools.enable_privacy_scraping
    
    def execute(self, website_url: str, extract_dpo: bool = True) -> str:
        if not self.is_enabled():
            return "Privacy scraping tool is disabled in configuration"

        try:
            return self.tool._run(website_url, extract_dpo)
        except Exception as e:
            self.logger.error(f"Privacy scraping tool error: {e}")
            return f"Error: {str(e)}"


class LLMToolManager(LoggerMixin):
    """Manager for configurable LLM tools."""
    
    def __init__(self):
        self.tools = {
            'web_search': ConfigurableWebSearchTool(),
            'domain_lookup': ConfigurableDomainLookupTool(),
            'business_search': ConfigurableBusinessSearchTool(),
            'privacy_scraping': ConfigurablePrivacyScrapingTool()
        }
        self.logger.info("LLM Tool Manager initialized")
    
    def get_enabled_tools(self) -> List[LLMTool]:
        """Get list of enabled tools."""
        return [tool for tool in self.tools.values() if tool.is_enabled()]
    
    def get_tool_descriptions(self) -> Dict[str, str]:
        """Get descriptions of all enabled tools."""
        return {
            name: tool.get_description() 
            for name, tool in self.tools.items() 
            if tool.is_enabled()
        }
    
    def execute_tool(self, tool_name: str, **kwargs) -> str:
        """Execute a specific tool by name."""
        if tool_name not in self.tools:
            return f"Unknown tool: {tool_name}"
        
        tool = self.tools[tool_name]
        if not tool.is_enabled():
            return f"Tool {tool_name} is disabled in configuration"
        
        return tool.execute(**kwargs)
    
    def get_tool_usage_prompt(self) -> str:
        """Generate prompt text describing available tools."""
        enabled_tools = self.get_enabled_tools()
        if not enabled_tools:
            return "No tools are currently enabled."
        
        tool_descriptions = []
        for tool in enabled_tools:
            tool_descriptions.append(f"- {tool.get_name()}: {tool.get_description()}")
        
        return "Available tools:\n" + "\n".join(tool_descriptions)


# Global tool manager instance
_tool_manager: Optional[LLMToolManager] = None


def get_tool_manager() -> LLMToolManager:
    """Get the global tool manager instance."""
    global _tool_manager
    if _tool_manager is None:
        _tool_manager = LLMToolManager()
    return _tool_manager


def reset_tool_manager():
    """Reset the global tool manager instance."""
    global _tool_manager
    _tool_manager = None
