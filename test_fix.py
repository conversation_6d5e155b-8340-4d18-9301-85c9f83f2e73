#!/usr/bin/env python3
"""Test script to verify the Pydantic fix for web tools."""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_tool_initialization():
    """Test that web tools can be initialized without Pydantic errors."""
    
    print("Testing Web Tool Initialization")
    print("=" * 40)
    
    try:
        from agents.tools import WebScrapingTool, DomainLookupTool, BusinessSearchTool, PrivacyPolicyFinderTool
        
        print("1. Testing WebScrapingTool...")
        web_scraper = WebScrapingTool()
        print("   ✓ WebScrapingTool initialized successfully")
        
        print("2. Testing DomainLookupTool...")
        domain_lookup = DomainLookupTool()
        print("   ✓ DomainLookupTool initialized successfully")
        
        print("3. Testing BusinessSearchTool...")
        business_search = BusinessSearchTool()
        print("   ✓ BusinessSearchTool initialized successfully")
        
        print("4. Testing PrivacyPolicyFinderTool...")
        privacy_finder = PrivacyPolicyFinderTool()
        print("   ✓ PrivacyPolicyFinderTool initialized successfully")
        
        print("\n✅ All tools initialized successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error initializing tools: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_chain_creation():
    """Test that the comprehensive analysis chain can be created."""
    
    print("\nTesting Comprehensive Chain Creation")
    print("=" * 40)
    
    try:
        from config.settings import set_environment
        from config.logging_config import setup_logging
        from core.llm_chains import LLMChainFactory
        
        # Set up environment
        set_environment('local')
        setup_logging(log_level='INFO', environment='local')
        
        print("1. Creating LLM...")
        llm = LLMChainFactory.create_llm()
        print("   ✓ LLM created successfully")
        
        print("2. Creating comprehensive analysis chain...")
        chain = LLMChainFactory.create_comprehensive_email_analysis_chain(llm)
        print("   ✓ Comprehensive analysis chain created successfully")
        
        print("\n✅ Chain creation test passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error creating chain: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_processor_initialization():
    """Test that the EmailProcessor can be initialized with the new chain."""
    
    print("\nTesting EmailProcessor Initialization")
    print("=" * 40)
    
    try:
        from config.settings import set_environment
        from config.logging_config import setup_logging
        from core.processor import EmailProcessor
        
        # Set up environment
        set_environment('local')
        setup_logging(log_level='INFO', environment='local')
        
        print("1. Initializing EmailProcessor...")
        processor = EmailProcessor(user_email="<EMAIL>")
        print("   ✓ EmailProcessor initialized successfully")
        
        print("2. Checking comprehensive analysis chain...")
        if hasattr(processor, 'comprehensive_analysis_chain'):
            print("   ✓ Comprehensive analysis chain is available")
        else:
            print("   ❌ Comprehensive analysis chain is missing")
            return False
        
        print("\n✅ Processor initialization test passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error initializing processor: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Email Scanner - Fix Verification Test")
    print("=" * 50)
    
    success = True
    
    # Test tool initialization
    if not test_tool_initialization():
        success = False
    
    # Test chain creation
    if not test_comprehensive_chain_creation():
        success = False
    
    # Test processor initialization
    if not test_processor_initialization():
        success = False
    
    if success:
        print("\n🎉 All tests passed! The fix is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
