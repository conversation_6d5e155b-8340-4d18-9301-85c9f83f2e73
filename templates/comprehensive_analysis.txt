Analyze this email and provide comprehensive business information in JSON format.

Email Information:
From: {from_field}
Subject: {subject}
Body: {body}

Instructions:
1. Categorize the email into one of these categories: business, social, marketing, personal, shopping, transaction, support, notification
2. Extract the business entity name from the email
3. Identify any business contact information (email, website, DPO email)
4. If business information is missing, you can use available tools to find:
   - Official business email address
   - Business website URL
   - Data Protection Officer (DPO) email address

Note: Tools may be available to help gather additional business information, but focus on extracting what you can from the email content first.

Return the result in this exact JSON format:
{{
  "email_category": "<category>",
  "business_entity_name": "<business_name>",
  "business_entity_email": "<business_email>",
  "business_entity_website": "<business_website>",
  "business_entity_dpo_email": "<dpo_email>"
}}

Guidelines:
- Use lowercase for email_category
- Extract clean business names without domains or suffixes
- For business_entity_email, prefer contact/info/support emails
- For business_entity_website, use full URLs with https://
- For business_entity_dpo_email, look for <PERSON><PERSON>, privacy officer, or data protection contacts
- If information cannot be found, use empty string ""
- Ensure valid JSON format

JSON Response:
