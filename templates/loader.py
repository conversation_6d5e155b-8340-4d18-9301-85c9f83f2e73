"""Template loader utility for managing LLM prompt templates."""
import os
import logging
from typing import Dict, Optional
from pathlib import Path

from config.logging_config import LoggerMixin
from utils.exceptions import handle_exception

logger = logging.getLogger(__name__)


class TemplateLoader(LoggerMixin):
    """Utility class for loading and managing prompt templates from files."""
    
    def __init__(self, templates_dir: Optional[str] = None):
        """Initialize the template loader.
        
        Args:
            templates_dir: Directory containing template files. Defaults to templates/ in project root.
        """
        if templates_dir is None:
            # Get the project root directory
            current_dir = Path(__file__).parent
            project_root = current_dir.parent
            templates_dir = project_root / "templates"
        
        self.templates_dir = Path(templates_dir)
        self._template_cache: Dict[str, str] = {}
        
        if not self.templates_dir.exists():
            raise FileNotFoundError(f"Templates directory not found: {self.templates_dir}")
        
        self.logger.info(f"Template<PERSON>oa<PERSON> initialized with directory: {self.templates_dir}")
    
    def load_template(self, template_name: str, use_cache: bool = True) -> str:
        """Load a template from file.
        
        Args:
            template_name: Name of the template file (without .txt extension)
            use_cache: Whether to use cached template if available
            
        Returns:
            Template content as string
            
        Raises:
            FileNotFoundError: If template file doesn't exist
            IOError: If template file can't be read
        """
        try:
            # Check cache first
            if use_cache and template_name in self._template_cache:
                self.logger.debug(f"Loading template '{template_name}' from cache")
                return self._template_cache[template_name]
            
            # Load from file
            template_path = self.templates_dir / f"{template_name}.txt"
            
            if not template_path.exists():
                raise FileNotFoundError(f"Template file not found: {template_path}")
            
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read().strip()
            
            # Cache the template
            if use_cache:
                self._template_cache[template_name] = template_content
            
            self.logger.debug(f"Loaded template '{template_name}' from file")
            return template_content
            
        except Exception as e:
            error = handle_exception(
                self.logger, 
                e, 
                f"Loading template '{template_name}'"
            )
            raise error
    
    def get_categorization_template(self) -> str:
        """Get the email categorization template."""
        return self.load_template("categorization")
    
    def get_categorization_example_template(self) -> str:
        """Get the email categorization example template."""
        return self.load_template("categorization_examples")
    
    def get_entity_extraction_template(self) -> str:
        """Get the entity extraction template."""
        return self.load_template("entity_extraction")
    
    def get_anonymization_template(self) -> str:
        """Get the email anonymization template."""
        return self.load_template("anonymization")

    def get_comprehensive_analysis_template(self) -> str:
        """Get the comprehensive email analysis template."""
        return self.load_template("comprehensive_analysis")
    

    
    def list_available_templates(self) -> list[str]:
        """List all available template files.
        
        Returns:
            List of template names (without .txt extension)
        """
        try:
            template_files = []
            for file_path in self.templates_dir.glob("*.txt"):
                template_files.append(file_path.stem)
            
            self.logger.debug(f"Found {len(template_files)} template files")
            return sorted(template_files)
            
        except Exception as e:
            error = handle_exception(
                self.logger, 
                e, 
                "Listing available templates"
            )
            return []
    
    def clear_cache(self):
        """Clear the template cache."""
        self._template_cache.clear()
        self.logger.debug("Template cache cleared")
    
    def reload_template(self, template_name: str) -> str:
        """Reload a template from file, bypassing cache.
        
        Args:
            template_name: Name of the template to reload
            
        Returns:
            Reloaded template content
        """
        # Remove from cache if present
        if template_name in self._template_cache:
            del self._template_cache[template_name]
        
        # Load fresh from file
        return self.load_template(template_name, use_cache=True)


# Global template loader instance
_template_loader: Optional[TemplateLoader] = None


def get_template_loader() -> TemplateLoader:
    """Get the global template loader instance.
    
    Returns:
        TemplateLoader instance
    """
    global _template_loader
    if _template_loader is None:
        _template_loader = TemplateLoader()
    return _template_loader
