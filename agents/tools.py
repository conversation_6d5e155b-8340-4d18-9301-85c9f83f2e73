"""LangChain tools for business identification and web scraping."""
import logging
import re
import socket
from typing import Dict, Any, List, Type
from urllib.parse import urlparse, urljoin
import requests
from bs4 import BeautifulSoup

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field


logger = logging.getLogger(__name__)


class WebScrapingInput(BaseModel):
    """Input for web scraping tool."""
    url: str = Field(description="URL to scrape")
    extract_contacts: bool = Field(default=True, description="Whether to extract contact information")


class DomainLookupInput(BaseModel):
    """Input for domain lookup tool."""
    email_or_domain: str = Field(description="Email address or domain to lookup")


class BusinessSearchInput(BaseModel):
    """Input for business search tool."""
    business_name: str = Field(description="Business name to search for")
    additional_info: str = Field(default="", description="Additional context information")


class WebScrapingTool(BaseTool):
    """Tool for scraping web pages and extracting business information."""

    name: str = "web_scraping"
    description: str = "Scrape web pages to extract business information, contact details, and other relevant data"
    args_schema: Type[BaseModel] = WebScrapingInput
    timeout: int = Field(default=30, description="Request timeout in seconds")

    def __init__(self, timeout: int = 30, **kwargs):
        super().__init__(timeout=timeout, **kwargs)
        # Initialize session and logger after parent initialization
        object.__setattr__(self, 'session', requests.Session())
        object.__setattr__(self, 'logger', logging.getLogger(self.__class__.__name__))

        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def _run(self, url: str, extract_contacts: bool = True) -> str:
        """Scrape a web page and extract relevant information."""
        try:
            self.logger.info(f"Scraping URL: {url}")
            
            # Normalize URL
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract basic information
            result = {
                'url': url,
                'title': self._extract_title(soup),
                'description': self._extract_description(soup),
                'business_info': self._extract_business_info(soup),
            }
            
            if extract_contacts:
                result['contacts'] = self._extract_contact_info(response.text)
            
            return str(result)
            
        except Exception as e:
            error_msg = f"Error scraping {url}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract page title."""
        title_tag = soup.find('title')
        return title_tag.get_text().strip() if title_tag else ""
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extract page description."""
        desc_tag = soup.find('meta', attrs={'name': 'description'})
        if desc_tag:
            return desc_tag.get('content', '').strip()
        
        # Fallback to first paragraph
        p_tag = soup.find('p')
        return p_tag.get_text().strip()[:200] if p_tag else ""
    
    def _extract_business_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract business-specific information."""
        info = {}
        
        # Look for structured data
        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        for script in json_ld_scripts:
            try:
                import json
                data = json.loads(script.string)
                if isinstance(data, dict) and data.get('@type') in ['Organization', 'LocalBusiness']:
                    info['structured_data'] = {
                        'name': data.get('name'),
                        'address': data.get('address'),
                        'telephone': data.get('telephone'),
                        'email': data.get('email'),
                        'url': data.get('url')
                    }
            except:
                continue
        
        # Look for common business indicators
        text_content = soup.get_text().lower()
        if any(keyword in text_content for keyword in ['about us', 'company', 'business', 'services']):
            info['has_business_content'] = True
        
        return info
    
    def _extract_contact_info(self, text: str) -> Dict[str, List[str]]:
        """Extract contact information from the page."""
        contacts = {
            'emails': [],
            'phones': [],
            'addresses': []
        }
        
        # Extract emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        contacts['emails'] = list(set(emails))
        
        # Extract phone numbers
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # US format
            r'\b\(\d{3}\)\s*\d{3}[-.]?\d{4}\b',  # (*************
            r'\b\+\d{1,3}[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9}\b'  # International
        ]
        
        for pattern in phone_patterns:
            phones = re.findall(pattern, text)
            contacts['phones'].extend(phones)
        
        contacts['phones'] = list(set(contacts['phones']))
        
        return contacts


class DomainLookupTool(BaseTool):
    """Tool for looking up domain information and finding business websites."""

    name: str = "domain_lookup"
    description: str = "Look up domain information from email addresses and find associated business websites"
    args_schema: Type[BaseModel] = DomainLookupInput
    timeout: int = Field(default=10, description="Request timeout in seconds")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'logger', logging.getLogger(self.__class__.__name__))
    
    def _run(self, email_or_domain: str) -> str:
        """Look up domain information."""
        try:
            # Extract domain from email if needed
            if '@' in email_or_domain:
                domain = email_or_domain.split('@')[1]
            else:
                domain = email_or_domain
            
            self.logger.info(f"Looking up domain: {domain}")
            
            result = {
                'domain': domain,
                'website_urls': [],
                'is_accessible': False
            }
            
            # Try common website URLs
            common_urls = [
                f"https://www.{domain}",
                f"https://{domain}",
                f"http://www.{domain}",
                f"http://{domain}"
            ]
            
            for url in common_urls:
                try:
                    response = requests.head(url, timeout=self.timeout, allow_redirects=True)
                    if response.status_code == 200:
                        result['website_urls'].append(url)
                        result['is_accessible'] = True
                        break
                except:
                    continue
            
            # Try to resolve domain
            try:
                socket.gethostbyname(domain)
                result['dns_resolvable'] = True
            except:
                result['dns_resolvable'] = False
            
            return str(result)
            
        except Exception as e:
            error_msg = f"Error looking up domain {email_or_domain}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"


class BusinessSearchTool(BaseTool):
    """Tool for searching business information."""

    name: str = "business_search"
    description: str = "Search for business information using various APIs and web scraping techniques"
    args_schema: Type[BaseModel] = BusinessSearchInput

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'logger', logging.getLogger(self.__class__.__name__))

    def _run(self, business_name: str, additional_info: str = "") -> str:
        """Search for business information."""
        try:
            self.logger.info(f"Searching for business: {business_name}")

            # Construct search query
            query = business_name
            if additional_info:
                query += f" {additional_info}"

            result = {
                'business_name': business_name,
                'search_query': query,
                'suggested_searches': [
                    f"{business_name} official website",
                    f"{business_name} contact information",
                    f"{business_name} privacy policy",
                    f"{business_name} data protection officer"
                ],
                'note': 'This tool would integrate with search APIs in production'
            }

            return str(result)

        except Exception as e:
            error_msg = f"Error searching for business {business_name}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"


class PrivacyPolicyFinderTool(BaseTool):
    """Tool for finding and analyzing privacy policies."""

    name: str = "privacy_policy_finder"
    description: str = "Find privacy policy URLs and extract DPO contact information"
    args_schema: Type[BaseModel] = WebScrapingInput
    timeout: int = Field(default=15, description="Request timeout in seconds")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'logger', logging.getLogger(self.__class__.__name__))

    # Define privacy URL patterns as class attribute
    privacy_url_patterns: List[str] = [
        '/privacy',
        '/privacy-policy',
        '/privacy.html',
        '/legal/privacy',
        '/privacy-notice',
        '/privacypolicy',
        '/terms-privacy',
        '/legal/privacy-policy'
    ]
    
    def _run(self, url: str, extract_contacts: bool = True) -> str:
        """Find privacy policy and extract DPO information."""
        try:
            self.logger.info(f"Finding privacy policy for: {url}")
            
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            parsed_url = urlparse(url)
            base_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            result = {
                'base_url': url,
                'privacy_policy_url': None,
                'dpo_contacts': [],
                'privacy_contacts': []
            }
            
            # Try common privacy policy URL patterns
            for pattern in self.privacy_url_patterns:
                privacy_url = urljoin(base_domain, pattern)
                
                try:
                    response = requests.head(privacy_url, timeout=self.timeout)
                    if response.status_code == 200:
                        result['privacy_policy_url'] = privacy_url

                        if extract_contacts:
                            # Get the full page to extract contacts
                            full_response = requests.get(privacy_url, timeout=self.timeout)
                            if full_response.status_code == 200:
                                contacts = self._extract_dpo_contacts(full_response.text)
                                result.update(contacts)
                        break
                except:
                    continue
            
            return str(result)
            
        except Exception as e:
            error_msg = f"Error finding privacy policy for {url}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
    
    def _extract_dpo_contacts(self, html_content: str) -> Dict[str, List[str]]:
        """Extract DPO contact information from privacy policy."""
        contacts = {
            'dpo_contacts': [],
            'privacy_contacts': []
        }

        # Extract emails that might be DPO-related
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        all_emails = re.findall(email_pattern, html_content, re.IGNORECASE)

        for email in all_emails:
            email_lower = email.lower()
            if any(keyword in email_lower for keyword in ['dpo', 'privacy', 'data', 'legal']):
                contacts['dpo_contacts'].append(email)
            else:
                contacts['privacy_contacts'].append(email)

        return contacts


def get_business_identification_tools() -> List[BaseTool]:
    """Get all business identification tools.
    
    Returns:
        List of LangChain tools for business identification
    """
    return [
        WebScrapingTool(),
        DomainLookupTool(),
        BusinessSearchTool(),
        PrivacyPolicyFinderTool()
    ]
